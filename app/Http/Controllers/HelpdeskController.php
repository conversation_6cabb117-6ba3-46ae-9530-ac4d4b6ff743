<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pengaduan;
use App\Models\Paket;
use App\Models\Customer;
use App\Models\User;
use App\Models\Status;

class HelpdeskController extends Controller
{
    public function dataPengaduan()
    {
        return view('Helpdesk.data-pengaduan', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'pengaduan' => Pengaduan::all(),
        ]);
    }

    /**
     * Get the latest pengaduan data for AJAX polling
     */
    public function getPengaduanData(Request $request)
    {
        // Get the timestamp of the last check (if provided)
        $lastCheck = $request->input('last_check');

        // Get all pengaduan data with relationships
        $pengaduan = Pengaduan::with(['customer', 'pengaduan', 'status', 'teknisi'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Check for new entries since last check
        $newEntries = [];
        if ($lastCheck) {
            $lastCheckTime = \Carbon\Carbon::parse($lastCheck);
            $newEntries = $pengaduan->filter(function($item) use ($lastCheckTime) {
                return $item->created_at > $lastCheckTime ||
                       $item->updated_at > $lastCheckTime;
            })->values();
        }

        return response()->json([
            'success' => true,
            'data' => $pengaduan,
            'count' => $pengaduan->count(),
            'new_entries' => $newEntries,
            'has_new' => count($newEntries) > 0,
            'timestamp' => now()->format('Y-m-d H:i:s')
        ]);
    }

    public function antrian()
    {
        return view('Helpdesk.data-antrian-helpdesk', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'paket' => Paket::all(),
            'customer' => Customer::whereIn('status_id', [1, 2, 5])
                ->where('logistik_id', auth()->user()->id)
                ->get(),
        ]);
    }

    // Store
    public function addAntrian(Request $request)
    {
        $request->validate([
            'nama_customer' => 'required',
            'no_hp' => 'required',
            'alamat' => 'required',
            'gps' => 'required',
            'paket_id' => 'required',
        ]);

        if ($request->hasFile('identitas_file')) {
            $ktp = $request->file('identitas_file');
            $fileName = time() . '_' . str_replace(' ', '_', $ktp->getClientOriginalName());
            $ktp->move(public_path('uploads/identitas'), $fileName);
            $identitas_file = 'uploads/identitas/' . $fileName;
        } else {
            $identitas_file = null;
        }

        $data = Customer::create([
            'nama_customer' => $request->nama_customer,
            'no_hp' => $request->no_hp,
            'alamat' => $request->alamat,
            'gps' => $request->gps,
            'paket_id' => $request->paket_id,
            'status_id' => 1,
            'logistik_id' => auth()->user()->id,
            'no_identitas' => $request->no_identitas,
            'email' => $request->email,
            'teknisi_id' => null,
            'identitas' => $identitas_file,
            'created_at' => $request->tanggal_reg,
        ]);

        User::create([
            'name' => $request->nama_customer,
            'email' => $request->email,
            'password' => bcrypt('123456'),
            'roles_id' => 8,
        ]);

        if ($data) {
            return redirect()->back()->with('success', 'Antrian created successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to create antrian');
        }
    }

    /**
     * Display the details of a specific queue item
     */
    public function detailAntrian($id)
    {
        $customer = Customer::with(['paket', 'status', 'logistik', 'teknisi', 'lokasi'])
            ->findOrFail($id);

        return view('Helpdesk.detail-antrian-helpdesk', [
            'users' => auth()->user(),
            'roles' => auth()->user()->roles,
            'customer' => $customer,
            'status' => Status::all(),
            'paket' => Paket::all(),
        ]);
    }

    /**
     * Update the specified customer in storage.
     */
    public function updateAntrian(Request $request, $id)
    {
        $request->validate([
            'nama_customer' => 'required',
            'no_hp' => 'required',
            'alamat' => 'required',
            'gps' => 'required',
        ]);

        $customer = Customer::findOrFail($id);

        $customer->update([
            'nama_customer' => $request->nama_customer,
            'email' => $request->email,
            'no_hp' => $request->no_hp,
            'alamat' => $request->alamat,
            'gps' => $request->gps,
            'no_identitas' => $request->no_identitas,
            'paket_id' => $request->paket_id,
            'status_id' => $request->status_id,
        ]);

        // Update the associated user if email has changed
        if ($request->email && $customer->email != $request->email) {
            $user = User::where('email', $customer->email)->first();
            if ($user) {
                $user->update([
                    'name' => $request->nama_customer,
                    'email' => $request->email,
                ]);
            }
        }

        return redirect()->route('detail-antrian-helpdesk', $id)
            ->with('success', 'Data pelanggan berhasil diperbarui');
    }
}
